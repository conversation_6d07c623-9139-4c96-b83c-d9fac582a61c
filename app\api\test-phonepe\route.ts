import { NextResponse } from 'next/server';
import { testPhonePeAPI, logPhonePeConfig } from '@/config/phonepe';

export async function GET() {
  try {
    console.log('=== PhonePe API Test Started ===');
    
    // Log current configuration
    logPhonePeConfig();
    
    // Test API connectivity
    const testResult = await testPhonePeAPI();
    
    console.log('PhonePe API Test Result:', testResult);
    console.log('=== PhonePe API Test Completed ===');
    
    return NextResponse.json({
      success: testResult.success,
      message: testResult.message,
      details: testResult.details,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    console.error('PhonePe API Test Error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

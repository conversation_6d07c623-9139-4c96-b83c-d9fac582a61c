import { NextResponse } from 'next/server';
import { PHONEPE_CONFIG } from '@/config/phonepe';

export async function GET() {
  return NextResponse.json({
    merchantId: PHONEPE_CONFIG.MERCHANT_ID,
    saltIndex: PH<PERSON><PERSON>E_CONFIG.SALT_INDEX,
    environment: PHONEPE_CONFIG.ENVIRONMENT,
    isTestMode: PHONEPE_CONFIG.IS_TEST_MODE,
    saltKeyLength: PHONEPE_CONFIG.SALT_KEY?.length || 0,
    saltKeyPreview: PHONEPE_CONFIG.SALT_KEY?.substring(0, 10) + '...',
    envVars: {
      PHONEPE_TEST_MERCHANT_ID: process.env.PHONEPE_TEST_MERCHANT_ID,
      PHONEPE_TEST_SALT_KEY: process.env.PHONEPE_TEST_SALT_KEY?.substring(0, 10) + '...',
      PHONEPE_TEST_SALT_INDEX: process.env.PHONEPE_TEST_SALT_INDEX,
      PHONEPE_ENVIRONMENT: process.env.PHONEPE_ENVIRONMENT,
    }
  });
}
